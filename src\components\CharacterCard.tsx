import React from 'react';
import { Card } from "@/components/ui/card";
import { X } from 'lucide-react';

interface Character {
  id: number;
  name: string;
  image: string;
}

interface CharacterCardProps {
  character: Character;
  isEliminated: boolean;
  onEliminate: () => void;
  currentPlayer: 1 | 2;
}

export const CharacterCard: React.FC<CharacterCardProps> = ({
  character,
  isEliminated,
  onEliminate,
  currentPlayer
}) => {
  return (
    <Card 
      className={`relative overflow-hidden transition-all duration-300 cursor-pointer group ${
        isEliminated 
          ? 'opacity-30 grayscale' 
          : 'hover:scale-105 hover:shadow-lg'
      }`}
      onClick={!isEliminated ? onEliminate : undefined}
    >
      {/* Image */}
      <div className="aspect-square relative">
        <img
          src={character.image}
          alt={character.name}
          className="w-full h-full object-cover"
        />
        
        {/* Overlay */}
        <div className={`
          absolute inset-0 bg-gradient-to-t from-black/50 to-transparent
          ${!isEliminated ? 'opacity-0 group-hover:opacity-100' : ''}
          transition-opacity duration-300
        `} />
        
        {/* Elimination indicator */}
        {isEliminated && (
          <div className="absolute inset-0 flex items-center justify-center bg-character-eliminated/50">
            <X className="w-12 h-12 text-white" />
          </div>
        )}
        
        {/* Hover hint */}
        {!isEliminated && (
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className={`
              px-3 py-1 rounded-full text-white text-sm font-medium
              ${currentPlayer === 1 ? 'bg-player1' : 'bg-player2'}
            `}>
              Elimina
            </div>
          </div>
        )}
      </div>
      
      {/* Name */}
      <div className="p-3">
        <h3 className={`
          font-semibold text-center transition-colors duration-300
          ${isEliminated ? 'text-character-eliminated' : 'text-foreground'}
        `}>
          {character.name}
        </h3>
      </div>
    </Card>
  );
};