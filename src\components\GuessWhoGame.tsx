import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RefreshCw, Users, Eye, EyeOff } from 'lucide-react';
import { CharacterCard } from './CharacterCard';
import { PlayerSelection } from './PlayerSelection';

import agostinoImg from '@/assets/agostino.png';
import dalilaImg from '@/assets/dalila.png';
import francescoImg from '@/assets/francesco.png';
import giulianoImg from '@/assets/giuliano.png';
import mammaImg from '@/assets/mamma.png';
import nonnaLinaImg from '@/assets/nonna-lina.png';
import nonnaSilviaImg from '@/assets/nonna-silvia.png';
import papaImg from '@/assets/papà.png';
import riccardoImg from '@/assets/riccardo.png';
import victoriaImg from '@/assets/victoria.png';
import ziaGiuliaImg from '@/assets/zia-giulia.png';
import ziaSaraImg from '@/assets/zia-sara.png';

// Characters data
const characters = [
  { id: 1, name: 'Agostino', image: agostinoImg },
  { id: 2, name: 'Dalila', image: dalilaImg },
  { id: 3, name: 'Francesco', image: francescoImg },
  { id: 4, name: 'Giuliano', image: giulianoImg },
  { id: 5, name: 'Mamma', image: mammaImg },
  { id: 6, name: 'Nonna Lina', image: nonnaLinaImg },
  { id: 7, name: 'Nonna Silvia', image: nonnaSilviaImg },
  { id: 8, name: 'Papà', image: papaImg },
  { id: 9, name: 'Riccardo', image: riccardoImg },
  { id: 10, name: 'Victoria', image: victoriaImg },
  { id: 11, name: 'Zia Giulia', image: ziaGiuliaImg },
  { id: 12, name: 'Zia Sara', image: ziaSaraImg }
];

type GamePhase = 'selection' | 'playing';

export const GuessWhoGame = () => {
  const [gamePhase, setGamePhase] = useState<GamePhase>('selection');
  const [currentPlayer, setCurrentPlayer] = useState<1 | 2>(1);
  const [player1Selection, setPlayer1Selection] = useState<number | null>(null);
  const [player2Selection, setPlayer2Selection] = useState<number | null>(null);
  const [eliminatedByPlayer1, setEliminatedByPlayer1] = useState<Set<number>>(new Set());
  const [eliminatedByPlayer2, setEliminatedByPlayer2] = useState<Set<number>>(new Set());
  const [showSelections, setShowSelections] = useState(false);

  const resetGame = () => {
    setGamePhase('selection');
    setCurrentPlayer(1);
    setPlayer1Selection(null);
    setPlayer2Selection(null);
    setEliminatedByPlayer1(new Set());
    setEliminatedByPlayer2(new Set());
    setShowSelections(false);
  };

  const handleCharacterSelect = (characterId: number) => {
    if (currentPlayer === 1 && !player1Selection) {
      setPlayer1Selection(characterId);
    } else if (currentPlayer === 2 && !player2Selection) {
      setPlayer2Selection(characterId);
    }
  };

  const startGame = () => {
    if (player1Selection && player2Selection) {
      setGamePhase('playing');
      setCurrentPlayer(1);
    }
  };

  const switchTurn = () => {
    setCurrentPlayer(currentPlayer === 1 ? 2 : 1);
  };

  const eliminateCharacter = (characterId: number) => {
    if (currentPlayer === 1) {
      setEliminatedByPlayer1(prev => new Set([...prev, characterId]));
    } else {
      setEliminatedByPlayer2(prev => new Set([...prev, characterId]));
    }
  };

  const toggleSelectionVisibility = () => {
    setShowSelections(!showSelections);
  };

  if (gamePhase === 'selection') {
    return (
      <div className="min-h-screen bg-game-bg p-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-player1 to-player2 bg-clip-text text-transparent">
              Indovina Chi?
            </h1>
            <p className="text-lg text-muted-foreground mb-6">
              Ogni giocatore deve scegliere un personaggio da far indovinare all'avversario
            </p>
          </div>

          <PlayerSelection
            currentPlayer={currentPlayer}
            characters={characters}
            player1Selection={player1Selection}
            player2Selection={player2Selection}
            onCharacterSelect={handleCharacterSelect}
            onNextPlayer={() => setCurrentPlayer(2)}
            onStartGame={startGame}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-game-bg p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-player1 to-player2 bg-clip-text text-transparent">
              Indovina Chi?
            </h1>
            <Button
              onClick={toggleSelectionVisibility}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              {showSelections ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              {showSelections ? 'Nascondi' : 'Mostra'} Selezioni
            </Button>
          </div>
          <Button onClick={resetGame} variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Nuova Partita
          </Button>
        </div>

        {/* Current Player Indicator */}
        <Card className="p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Badge 
                className={`text-lg px-4 py-2 ${
                  currentPlayer === 1 
                    ? 'bg-player1 text-player1-foreground' 
                    : 'bg-player2 text-player2-foreground'
                }`}
              >
                <Users className="w-4 h-4 mr-2" />
                Turno di Giocatore {currentPlayer}
              </Badge>
              {showSelections && (
                <div className="text-sm text-muted-foreground">
                  <span className="text-player1">Giocatore 1:</span> {characters.find(c => c.id === player1Selection)?.name} | 
                  <span className="text-player2 ml-2">Giocatore 2:</span> {characters.find(c => c.id === player2Selection)?.name}
                </div>
              )}
            </div>
            <Button 
              onClick={switchTurn}
              className={`${
                currentPlayer === 1 
                  ? 'bg-player1 hover:bg-player1/90 text-player1-foreground' 
                  : 'bg-player2 hover:bg-player2/90 text-player2-foreground'
              }`}
            >
              Cambia Turno
            </Button>
          </div>
        </Card>

        {/* Characters Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {characters.map((character) => {
            const isEliminatedByCurrentPlayer = currentPlayer === 1 
              ? eliminatedByPlayer1.has(character.id)
              : eliminatedByPlayer2.has(character.id);
            
            return (
              <CharacterCard
                key={character.id}
                character={character}
                isEliminated={isEliminatedByCurrentPlayer}
                onEliminate={() => eliminateCharacter(character.id)}
                currentPlayer={currentPlayer}
              />
            );
          })}
        </div>

        {/* Game Instructions */}
        <Card className="mt-6 p-4">
          <h3 className="font-semibold mb-2">Come si gioca:</h3>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Fate domande a turno per eliminare i personaggi che non corrispondono</li>
            <li>• Cliccate su un personaggio per eliminarlo dalla vostra vista</li>
            <li>• Il primo che indovina il personaggio dell'avversario vince!</li>
            <li>• Usate il pulsante "Cambia Turno" per alternare i giocatori</li>
          </ul>
        </Card>
      </div>
    </div>
  );
};