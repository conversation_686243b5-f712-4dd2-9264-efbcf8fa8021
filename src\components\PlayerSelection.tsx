import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, ArrowRight, Play } from 'lucide-react';

interface Character {
  id: number;
  name: string;
  image: string;
}

interface PlayerSelectionProps {
  currentPlayer: 1 | 2;
  characters: Character[];
  player1Selection: number | null;
  player2Selection: number | null;
  onCharacterSelect: (characterId: number) => void;
  onNextPlayer: () => void;
  onStartGame: () => void;
}

export const PlayerSelection: React.FC<PlayerSelectionProps> = ({
  currentPlayer,
  characters,
  player1Selection,
  player2Selection,
  onCharacterSelect,
  onNextPlayer,
  onStartGame
}) => {
  const canProceed = currentPlayer === 1 ? player1Selection !== null : player2Selection !== null;
  const allSelectionsComplete = player1Selection !== null && player2Selection !== null;

  return (
    <div className="space-y-6">
      {/* Current Player Indicator */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <Badge 
            className={`text-lg px-4 py-2 ${
              currentPlayer === 1 
                ? 'bg-player1 text-player1-foreground' 
                : 'bg-player2 text-player2-foreground'
            }`}
          >
            <Users className="w-4 h-4 mr-2" />
            Giocatore {currentPlayer} - Scegli il tuo personaggio
          </Badge>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <div className={`w-3 h-3 rounded-full ${player1Selection ? 'bg-player1' : 'bg-gray-300'}`} />
            <span>G1</span>
            <div className={`w-3 h-3 rounded-full ${player2Selection ? 'bg-player2' : 'bg-gray-300'}`} />
            <span>G2</span>
          </div>
        </div>
      </Card>

      {/* Instructions */}
      <Card className="p-4">
        <p className="text-center text-muted-foreground">
          {currentPlayer === 1 && !player1Selection && "Giocatore 1, scegli il personaggio che l'avversario dovrà indovinare."}
          {currentPlayer === 1 && player1Selection && "Perfetto! Ora passa il dispositivo al Giocatore 2."}
          {currentPlayer === 2 && !player2Selection && "Giocatore 2, scegli il personaggio che l'avversario dovrà indovinare."}
          {currentPlayer === 2 && player2Selection && "Ottimo! Ora potete iniziare a giocare."}
        </p>
      </Card>

      {/* Character Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {characters.map((character) => {
          const isSelected = (currentPlayer === 1 && player1Selection === character.id) ||
                           (currentPlayer === 2 && player2Selection === character.id);
          const isDisabled = (currentPlayer === 1 && player1Selection !== null) ||
                           (currentPlayer === 2 && player2Selection !== null);

          return (
            <Card 
              key={character.id}
              className={`
                relative overflow-hidden cursor-pointer transition-all duration-300
                ${isSelected 
                  ? `ring-4 ${currentPlayer === 1 ? 'ring-player1' : 'ring-player2'}` 
                  : 'hover:scale-105 hover:shadow-lg'
                }
                ${isDisabled && !isSelected ? 'opacity-50 cursor-not-allowed' : ''}
              `}
              onClick={!isDisabled ? () => onCharacterSelect(character.id) : undefined}
            >
              <div className="aspect-square relative">
                <img
                  src={character.image}
                  alt={character.name}
                  className="w-full h-full object-cover"
                />
                
                {isSelected && (
                  <div className={`
                    absolute inset-0 flex items-center justify-center
                    ${currentPlayer === 1 ? 'bg-player1/20' : 'bg-player2/20'}
                  `}>
                    <div className={`
                      px-3 py-1 rounded-full text-white font-bold
                      ${currentPlayer === 1 ? 'bg-player1' : 'bg-player2'}
                    `}>
                      SCELTO
                    </div>
                  </div>
                )}
              </div>
              
              <div className="p-3">
                <h3 className="font-semibold text-center">{character.name}</h3>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center">
        {currentPlayer === 1 && canProceed && !allSelectionsComplete && (
          <Button 
            onClick={onNextPlayer}
            className="bg-player1 hover:bg-player1/90 text-player1-foreground flex items-center gap-2"
            size="lg"
          >
            Giocatore 2 <ArrowRight className="w-4 h-4" />
          </Button>
        )}
        
        {allSelectionsComplete && (
          <Button 
            onClick={onStartGame}
            className="bg-gradient-to-r from-player1 to-player2 text-white flex items-center gap-2"
            size="lg"
          >
            <Play className="w-4 h-4" />
            Inizia a Giocare!
          </Button>
        )}
      </div>
    </div>
  );
};